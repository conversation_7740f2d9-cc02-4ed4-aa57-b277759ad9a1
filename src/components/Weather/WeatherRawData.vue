<template>
  <div class="weather-raw-data">
    <!-- 地点选择器 -->
    <div v-if="locations.length > 1" class="location-selector">
      <div class="location-tabs">
        <button
          v-for="location in locations"
          :key="location"
          :class="['location-tab', { active: selectedLocation === location }]"
          @click="selectLocation(location)"
        >
          {{ location }}
        </button>
      </div>
    </div>

    <!-- 天气原始信息展示 -->
    <div v-if="currentWeatherData" class="weather-info">
      <div class="weather-header">
        <h3 class="location-name">{{ currentWeatherData.location.name }}</h3>
        <div class="location-source">
          来源：{{ currentWeatherData.location.person_name }} ({{ currentWeatherData.location.relationship }})
        </div>
      </div>

      <div class="weather-details">
        <!-- 主要天气信息 -->
        <div class="main-weather">
          <div class="temperature-section">
            <div class="current-temp">{{ currentWeatherData.weather.temperature }}°C</div>
            <div class="feels-like">体感 {{ currentWeatherData.weather.feelsLike }}°C</div>
            <div class="weather-desc">{{ currentWeatherData.weather.weather }}</div>
          </div>
        </div>

        <!-- 详细信息网格 -->
        <div class="weather-grid">
          <div class="weather-item">
            <div class="weather-label">湿度</div>
            <div class="weather-value">{{ currentWeatherData.weather.humidity }}</div>
          </div>
          <div class="weather-item">
            <div class="weather-label">风向</div>
            <div class="weather-value">{{ currentWeatherData.weather.windDirection }}</div>
          </div>
          <div class="weather-item">
            <div class="weather-label">风速</div>
            <div class="weather-value">{{ currentWeatherData.weather.windSpeed }}</div>
          </div>
          <div class="weather-item">
            <div class="weather-label">风力</div>
            <div class="weather-value">{{ currentWeatherData.weather.windPower }}</div>
          </div>
          <div class="weather-item">
            <div class="weather-label">气压</div>
            <div class="weather-value">{{ currentWeatherData.weather.pressure }}</div>
          </div>
          <div class="weather-item">
            <div class="weather-label">能见度</div>
            <div class="weather-value">{{ currentWeatherData.weather.visibility }}</div>
          </div>
          <div class="weather-item">
            <div class="weather-label">降水量</div>
            <div class="weather-value">{{ currentWeatherData.weather.precipitation }}</div>
          </div>
          <div class="weather-item">
            <div class="weather-label">云量</div>
            <div class="weather-value">{{ currentWeatherData.weather.cloud }}</div>
          </div>
          <div class="weather-item">
            <div class="weather-label">露点</div>
            <div class="weather-value">{{ currentWeatherData.weather.dewPoint }}</div>
          </div>
        </div>

        <!-- 更新时间 -->
        <div class="update-time">
          更新时间：{{ formatUpdateTime(currentWeatherData.weather.updateTime) }}
        </div>
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-else class="no-data">
      <div class="no-data-text">暂无天气数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { IWeatherDataItem } from '@/apis/memory';

// Props
interface IProps {
  weatherData: IWeatherDataItem[];
}

const props = defineProps<IProps>();

// 响应式数据
const selectedLocation = ref<string>('');

// 计算属性
const locations = computed(() => {
  return props.weatherData.map(item => item.location.name);
});

const currentWeatherData = computed(() => {
  if (!selectedLocation.value || !props.weatherData.length) {
    return props.weatherData[0] || null;
  }
  return props.weatherData.find(item => item.location.name === selectedLocation.value) || props.weatherData[0];
});

// 方法
const selectLocation = (location: string) => {
  selectedLocation.value = location;
};

const formatUpdateTime = (updateTime: string) => {
  try {
    const date = new Date(updateTime);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (error) {
    return updateTime;
  }
};

// 监听数据变化，自动选择第一个地点
watch(
  () => props.weatherData,
  (newData) => {
    if (newData.length > 0 && !selectedLocation.value) {
      selectedLocation.value = newData[0].location.name;
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.weather-raw-data {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 188, 212, 0.2);
}

// 地点选择器
.location-selector {
  margin-bottom: 24px;

  .location-tabs {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .location-tab {
      padding: 8px 16px;
      border: 2px solid rgba(0, 188, 212, 0.3);
      background: transparent;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 16px;
      color: #333;

      &:hover {
        border-color: #00bcd4;
        background: rgba(0, 188, 212, 0.1);
      }

      &.active {
        background: #00bcd4;
        color: white;
        border-color: #00bcd4;
      }
    }
  }
}

// 天气信息
.weather-info {
  .weather-header {
    margin-bottom: 24px;
    text-align: center;

    .location-name {
      font-size: 28px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }

    .location-source {
      font-size: 16px;
      color: #666;
    }
  }

  .weather-details {
    .main-weather {
      text-align: center;
      margin-bottom: 32px;
      padding: 24px;
      background: linear-gradient(135deg, rgba(0, 188, 212, 0.1), rgba(0, 188, 212, 0.05));
      border-radius: 12px;

      .temperature-section {
        .current-temp {
          font-size: 48px;
          font-weight: 700;
          color: #00bcd4;
          margin-bottom: 8px;
        }

        .feels-like {
          font-size: 18px;
          color: #666;
          margin-bottom: 12px;
        }

        .weather-desc {
          font-size: 24px;
          color: #333;
          font-weight: 500;
        }
      }
    }

    .weather-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
      margin-bottom: 24px;

      .weather-item {
        background: rgba(0, 188, 212, 0.05);
        padding: 16px;
        border-radius: 8px;
        text-align: center;
        border: 1px solid rgba(0, 188, 212, 0.1);

        .weather-label {
          font-size: 16px;
          color: #666;
          margin-bottom: 8px;
        }

        .weather-value {
          font-size: 20px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .update-time {
      text-align: center;
      font-size: 16px;
      color: #999;
      font-style: italic;
    }
  }
}

// 无数据状态
.no-data {
  text-align: center;
  padding: 40px;

  .no-data-text {
    font-size: 20px;
    color: #999;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .weather-raw-data {
    padding: 16px;
    margin-bottom: 16px;
  }

  .weather-info {
    .weather-header {
      .location-name {
        font-size: 24px;
      }

      .location-source {
        font-size: 14px;
      }
    }

    .weather-details {
      .main-weather {
        padding: 16px;

        .temperature-section {
          .current-temp {
            font-size: 36px;
          }

          .feels-like {
            font-size: 16px;
          }

          .weather-desc {
            font-size: 20px;
          }
        }
      }

      .weather-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 12px;

        .weather-item {
          padding: 12px;

          .weather-label {
            font-size: 14px;
          }

          .weather-value {
            font-size: 18px;
          }
        }
      }

      .update-time {
        font-size: 14px;
      }
    }
  }

  .location-selector {
    .location-tabs {
      .location-tab {
        font-size: 14px;
        padding: 6px 12px;
      }
    }
  }
}
</style>
