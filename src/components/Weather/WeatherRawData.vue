<template>
  <div class="weather-raw-data">
    <!-- 地点选择器 -->
    <div v-if="locations.length > 1" class="location-selector">
      <div class="location-tabs">
        <button
          v-for="location in locations"
          :key="location"
          :class="['location-tab', { active: selectedLocation === location }]"
          @click="selectLocation(location)"
        >
          {{ location }}
        </button>
      </div>
    </div>

    <!-- 天气原始信息展示 -->
    <div v-if="currentWeatherData" class="weather-info">
      <!-- 天气卡片 - 备忘录风格 -->
      <div class="weather-card">
        <!-- 第一行：地点天气情况 -->
        <div class="weather-title">
          {{ currentWeatherData.location.name }}天气情况
        </div>

        <!-- 第二行：主要天气信息 -->
        <div class="weather-summary">
          {{ currentWeatherData.weather.weather }}
          气温{{ currentWeatherData.weather.temperature }}℃
          体感{{ currentWeatherData.weather.feelsLike }}℃
          湿度{{ currentWeatherData.weather.humidity }}
          {{ currentWeatherData.weather.windDirection }}
          {{ currentWeatherData.weather.windPower }}
        </div>

        <!-- 详细信息（可展开） -->
        <div class="weather-details" :class="{ expanded: showDetails }">
          <div class="detail-item">
            <span class="detail-label">风速：</span>
            <span class="detail-value">{{ currentWeatherData.weather.windSpeed }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">气压：</span>
            <span class="detail-value">{{ currentWeatherData.weather.pressure }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">能见度：</span>
            <span class="detail-value">{{ currentWeatherData.weather.visibility }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">降水量：</span>
            <span class="detail-value">{{ currentWeatherData.weather.precipitation }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">云量：</span>
            <span class="detail-value">{{ currentWeatherData.weather.cloud }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">露点：</span>
            <span class="detail-value">{{ currentWeatherData.weather.dewPoint }}</span>
          </div>
        </div>

        <!-- 展开/收起按钮 -->
        <div class="toggle-details" @click="toggleDetails">
          {{ showDetails ? '收起详情' : '查看详情' }}
        </div>

        <!-- 数据来源和更新时间 -->
        <div class="weather-meta">
          <div class="data-source">
            来源：{{ currentWeatherData.location.person_name }} ({{ currentWeatherData.location.relationship }})
          </div>
          <div class="update-time">
            {{ formatUpdateTime(currentWeatherData.weather.updateTime) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-else class="no-data">
      <div class="no-data-text">暂无天气数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { IWeatherDataItem } from '@/apis/memory';

// Props
interface IProps {
  weatherData: IWeatherDataItem[];
}

const props = defineProps<IProps>();

// 响应式数据
const selectedLocation = ref<string>('');
const showDetails = ref<boolean>(false);

// 计算属性
const locations = computed(() => {
  return props.weatherData.map(item => item.location.name);
});

const currentWeatherData = computed(() => {
  if (!selectedLocation.value || !props.weatherData.length) {
    return props.weatherData[0] || null;
  }
  return props.weatherData.find(item => item.location.name === selectedLocation.value) || props.weatherData[0];
});

// 方法
const selectLocation = (location: string) => {
  selectedLocation.value = location;
};

const toggleDetails = () => {
  showDetails.value = !showDetails.value;
};

const formatUpdateTime = (updateTime: string) => {
  try {
    const date = new Date(updateTime);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (error) {
    return updateTime;
  }
};

// 监听数据变化，自动选择第一个地点
watch(
  () => props.weatherData,
  (newData) => {
    if (newData.length > 0 && !selectedLocation.value) {
      selectedLocation.value = newData[0].location.name;
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.weather-raw-data {
  padding: 0;
  margin-bottom: 20px;
}

// 地点选择器 - H5移动端优化
.location-selector {
  margin-bottom: 16px;

  .location-tabs {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;

    .location-tab {
      padding: 10px 20px;
      border: 2px solid rgba(0, 188, 212, 0.3);
      background: rgba(255, 255, 255, 0.8);
      border-radius: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 18px;
      font-weight: 500;
      color: #333;
      min-width: 80px;
      text-align: center;
      backdrop-filter: blur(10px);

      &:hover {
        border-color: #00bcd4;
        background: rgba(0, 188, 212, 0.1);
        transform: translateY(-2px);
      }

      &.active {
        background: linear-gradient(135deg, #00bcd4, #00acc1);
        color: white;
        border-color: #00bcd4;
        box-shadow: 0 4px 15px rgba(0, 188, 212, 0.3);
      }
    }
  }
}

// 天气信息
.weather-info {
  .weather-header {
    margin-bottom: 24px;
    text-align: center;

    .location-name {
      font-size: 28px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }

    .location-source {
      font-size: 16px;
      color: #666;
    }
  }

  .weather-details {
    .main-weather {
      text-align: center;
      margin-bottom: 32px;
      padding: 24px;
      background: linear-gradient(135deg, rgba(0, 188, 212, 0.1), rgba(0, 188, 212, 0.05));
      border-radius: 12px;

      .temperature-section {
        .current-temp {
          font-size: 48px;
          font-weight: 700;
          color: #00bcd4;
          margin-bottom: 8px;
        }

        .feels-like {
          font-size: 18px;
          color: #666;
          margin-bottom: 12px;
        }

        .weather-desc {
          font-size: 24px;
          color: #333;
          font-weight: 500;
        }
      }
    }

    .weather-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
      margin-bottom: 24px;

      .weather-item {
        background: rgba(0, 188, 212, 0.05);
        padding: 16px;
        border-radius: 8px;
        text-align: center;
        border: 1px solid rgba(0, 188, 212, 0.1);

        .weather-label {
          font-size: 16px;
          color: #666;
          margin-bottom: 8px;
        }

        .weather-value {
          font-size: 20px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .update-time {
      text-align: center;
      font-size: 16px;
      color: #999;
      font-style: italic;
    }
  }
}

// 无数据状态
.no-data {
  text-align: center;
  padding: 40px;

  .no-data-text {
    font-size: 20px;
    color: #999;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .weather-raw-data {
    padding: 16px;
    margin-bottom: 16px;
  }

  .weather-info {
    .weather-header {
      .location-name {
        font-size: 24px;
      }

      .location-source {
        font-size: 14px;
      }
    }

    .weather-details {
      .main-weather {
        padding: 16px;

        .temperature-section {
          .current-temp {
            font-size: 36px;
          }

          .feels-like {
            font-size: 16px;
          }

          .weather-desc {
            font-size: 20px;
          }
        }
      }

      .weather-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 12px;

        .weather-item {
          padding: 12px;

          .weather-label {
            font-size: 14px;
          }

          .weather-value {
            font-size: 18px;
          }
        }
      }

      .update-time {
        font-size: 14px;
      }
    }
  }

  .location-selector {
    .location-tabs {
      .location-tab {
        font-size: 14px;
        padding: 6px 12px;
      }
    }
  }
}

// 天气信息卡片 - 备忘录风格
.weather-info {
  .weather-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #00bcd4;
    backdrop-filter: blur(10px);

    .weather-title {
      font-size: 22px;
      font-weight: 600;
      color: #333;
      margin-bottom: 12px;
      line-height: 1.3;
    }

    .weather-summary {
      font-size: 20px;
      color: #555;
      line-height: 1.4;
      margin-bottom: 16px;
      padding: 12px 0;
      border-bottom: 1px solid rgba(0, 188, 212, 0.2);
    }

    .weather-details {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;

      &.expanded {
        max-height: 300px;
      }

      .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:last-child {
          border-bottom: none;
        }

        .detail-label {
          font-size: 18px;
          color: #666;
          font-weight: 500;
        }

        .detail-value {
          font-size: 18px;
          color: #333;
          font-weight: 600;
        }
      }
    }

    .toggle-details {
      text-align: center;
      padding: 12px 0;
      color: #00bcd4;
      font-size: 18px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      border-radius: 8px;
      margin: 8px 0;

      &:hover {
        background: rgba(0, 188, 212, 0.1);
        color: #00acc1;
      }

      &:active {
        transform: scale(0.98);
      }
    }

    .weather-meta {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid rgba(0, 188, 212, 0.2);

      .data-source {
        font-size: 16px;
        color: #666;
        margin-bottom: 8px;
      }

      .update-time {
        font-size: 16px;
        color: #999;
        font-style: italic;
      }
    }
  }
}

// 无数据状态
.no-data {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(10px);

  .no-data-text {
    font-size: 20px;
    color: #999;
  }
}

// 响应式适配 - H5移动端优化
@media (max-width: 768px) {
  .weather-raw-data {
    margin-bottom: 16px;
  }

  .location-selector {
    margin-bottom: 12px;

    .location-tabs {
      gap: 8px;

      .location-tab {
        padding: 8px 16px;
        font-size: 16px;
        min-width: 70px;
      }
    }
  }

  .weather-info {
    .weather-card {
      padding: 16px;
      border-radius: 12px;

      .weather-title {
        font-size: 20px;
        margin-bottom: 10px;
      }

      .weather-summary {
        font-size: 18px;
        margin-bottom: 12px;
        padding: 10px 0;
      }

      .weather-details {
        .detail-item {
          padding: 6px 0;

          .detail-label,
          .detail-value {
            font-size: 16px;
          }
        }
      }

      .toggle-details {
        font-size: 16px;
        padding: 10px 0;
      }

      .weather-meta {
        margin-top: 12px;
        padding-top: 12px;

        .data-source,
        .update-time {
          font-size: 14px;
        }
      }
    }
  }

  .no-data {
    padding: 30px 16px;

    .no-data-text {
      font-size: 18px;
    }
  }
}

// 超小屏幕适配
@media (max-width: 480px) {
  .location-selector {
    .location-tabs {
      .location-tab {
        padding: 6px 12px;
        font-size: 14px;
        min-width: 60px;
      }
    }
  }

  .weather-info {
    .weather-card {
      padding: 12px;

      .weather-title {
        font-size: 18px;
      }

      .weather-summary {
        font-size: 16px;
      }

      .weather-details {
        .detail-item {
          .detail-label,
          .detail-value {
            font-size: 14px;
          }
        }
      }

      .toggle-details {
        font-size: 14px;
      }

      .weather-meta {
        .data-source,
        .update-time {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
