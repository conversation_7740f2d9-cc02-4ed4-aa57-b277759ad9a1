<template>
  <div class="weather-container theme-background">
    <div class="weather-page">
      <!-- 头部区域 - Maintopbar组件 -->
      <Maintopbar
        :show-back-btn="false"
        :show-history-btn="false"
        :show-relationship-btn="false"
        :show-user-avatar="false"
        :show-assistant-avatar="false"
        :show-voice-btn="false"
        :show-add-chat-btn="false"
        :show-weather-btn="false"
        :show-memo-btn="false"
        :show-home-btn="true"
        :show-back-to-index-btn="true"
        :show-feature-intro="true"
        :is-chat-play="false"
        :user-loading="false"
        :current-mis-id="''"
        :get-random-color="() => '#ccc'"
        :get-avatar-letter="() => ''"
        :show-header-grad="false"
        @home="handleHome"
        @back-to-index="handleBackToIndex"
      />

      <!-- 中间内容区域 -->
      <div class="weather-content-container">
        <!-- 加载状态 -->
        <div v-if="weatherLoading" class="loading-container">
          <div class="loading-text">正在获取天气信息...</div>
        </div>

        <!-- 天气内容 -->
        <div v-else-if="weatherData" class="weather-sections">
          <!-- 原始天气信息组件 -->
          <WeatherRawData :weather-data="weatherData.weather_data" />

          <!-- 地点总结组件 - 预留位置 -->
          <!-- <WeatherLocationSummaries :location-summaries="weatherData.location_summaries" /> -->

          <!-- 完整天气提醒组件 - 预留位置 -->
          <!-- <WeatherLocationReminders :location-reminders="weatherData.location_reminders" /> -->
        </div>

        <!-- 错误状态 -->
        <div v-else-if="weatherError" class="error-container">
          <div class="error-text">{{ weatherError }}</div>
          <button class="retry-btn" @click="loadWeatherData">重试</button>
        </div>

        <!-- 初始状态 -->
        <div v-else class="initial-container">
          <div class="initial-text">点击下方输入框开始获取天气信息</div>
          <button class="get-weather-btn" @click="loadWeatherData">获取天气</button>
        </div>
      </div>

      <!-- 底部区域：输入框和老董假装说话 -->
      <div class="footer">
        <!-- 输入框 -->
        <form class="input-wrapper" action="" @submit.prevent="handleFormSubmit">
          <inputBar
            ref="inputBarRef"
            @voice-send="handleInputSend"
            @get-started="handleInputSend"
            @send="handleInputSend"
            @stop="handleStop"
            @recording-status="handleRecordingStatus"
          />
        </form>

        <!-- 老董假装说话样式 - 直接放在输入框下方 -->
        <div class="laodong-fake-speaking">
          <div class="fake-speaking-container">
            <div class="laodong-avatar">
              <img :src="dongtianqiImg" alt="董天奇头像" />
            </div>
            <div class="fake-speaking-content">
              <div class="fake-speaking-text">老董会根据您的问题给出专业建议</div>
              <div class="fake-speaking-dots">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话展示组件 -->
    <ChatDialog
      :visible="showChatDialog"
      :messages="chatMessages"
      :conversation-id="conversationId"
      :user-id="currentUserId"
      @close="handleCloseChatDialog"
      @send-message="handleChatDialogSend"
      @regenerate="handleRegenerate"
      @new-chat="clearChatSession"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import Maintopbar from '@/components/Maintopbar.vue';
import inputBar from '@/components/Chat/inputBar.vue';
import ChatDialog from '@/components/Dialogs/ChatDialog.vue';
import WeatherRawData from '@/components/Weather/WeatherRawData.vue';
import dongtianqiImg from '@/assets/assistant/dongtianqi.png';
import { getUserInfo } from '@/apis/common';
import { streamChat, createConversation, type IToolCall } from '@/apis/chat';
import { getComprehensiveWeather, type IComprehensiveWeatherResponse } from '@/apis/memory';
import { Typewriter } from '@/utils/typeWriter';

const router = useRouter();

// 用户信息
const currentUserId = ref('');

// 录音状态
const isRecording = ref(false);

// 聊天对话框相关
const showChatDialog = ref(false);
const chatMessages = ref<IChatStreamContent[]>([]);
const conversationId = ref('');

// 聊天状态
const isLoading = ref(false);
const abortController = ref<AbortController | null>(null);
const typewriter = ref<Typewriter | null>(null);

// 天气相关状态
const weatherData = ref<IComprehensiveWeatherResponse | null>(null);
const weatherLoading = ref(false);
const weatherError = ref<string>('');

// 输入框引用
const inputBarRef = ref();

// 处理回到首页
const handleBackToIndex = async () => {
  console.log('🏠 [weather.vue] 回到首页按钮点击');
  await router.push({ name: 'chat' });
};

// 处理home按钮点击
const handleHome = async () => {
  console.log('🏠 [weather.vue] home按钮点击');
  await router.push({ name: 'chat' });
};

// 处理表单提交
const handleFormSubmit = () => {
  // 阻止默认表单提交
};

// 处理输入发送
const handleInputSend = async (message: string) => {
  console.log('📤 [weather.vue] 发送消息:', message);
  if (message.trim() && !isLoading.value) {
    // 打开聊天对话框
    showChatDialog.value = true;

    // 确保有会话ID
    await ensureConversationId();

    // 发送消息
    await sendMessage(message);
  }
};

// 处理停止
const handleStop = () => {
  console.log('⏹️ [weather.vue] 停止操作');
};

// 处理录音状态变化
const handleRecordingStatus = (recording: boolean) => {
  isRecording.value = recording;
  console.log('🎤 [weather.vue] 录音状态变化:', recording);
};

// 处理关闭聊天对话框
const handleCloseChatDialog = () => {
  showChatDialog.value = false;
  console.log('❌ [weather.vue] 关闭聊天对话框');
};

// 处理聊天对话框发送消息
const handleChatDialogSend = async (message: string) => {
  console.log('💬 [weather.vue] 聊天对话框发送消息:', message);
  if (message.trim() && !isLoading.value) {
    await sendMessage(message);
  }
};

// 处理重新生成
const handleRegenerate = async (messageData: IChatStreamContent) => {
  console.log('🔄 [weather.vue] 重新生成:', messageData);
  if (isLoading.value) return;

  // 找到要重新生成的消息在数组中的位置
  const messageIndex = chatMessages.value.findIndex(msg => msg.key === messageData.key);
  if (messageIndex === -1) return;

  // 删除该消息及其后面的所有消息
  chatMessages.value = chatMessages.value.slice(0, messageIndex);

  // 找到上一条用户消息
  const lastUserMessage = [...chatMessages.value].reverse().find(msg => msg.role === 'user');
  if (lastUserMessage) {
    await sendMessage(lastUserMessage.content);
  }
};

// 清理聊天会话
const clearChatSession = () => {
  chatMessages.value = [];
  conversationId.value = '';
  isLoading.value = false;
  if (abortController.value) {
    abortController.value.abort();
    abortController.value = null;
  }
  if (typewriter.value) {
    typewriter.value.stop();
    typewriter.value = null;
  }
  console.log('🧹 [weather.vue] 清理聊天会话');
};

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const userInfo = await getUserInfo();
    currentUserId.value = userInfo.login || 'unknown_user';
    console.log('👤 [weather.vue] 用户信息加载成功:', currentUserId.value);
  } catch (error) {
    console.error('❌ [weather.vue] 加载用户信息失败:', error);
    currentUserId.value = 'unknown_user';
  }
};

// 加载天气数据
const loadWeatherData = async () => {
  if (!currentUserId.value || currentUserId.value === 'unknown_user') {
    weatherError.value = '请先登录获取用户信息';
    return;
  }

  weatherLoading.value = true;
  weatherError.value = '';

  try {
    console.log('🌤️ [weather.vue] 开始获取天气数据');
    const response = await getComprehensiveWeather({
      user_id: currentUserId.value,
    });

    weatherData.value = response;
    console.log('✅ [weather.vue] 天气数据获取成功:', response);
  } catch (error) {
    console.error('❌ [weather.vue] 获取天气数据失败:', error);
    weatherError.value = '获取天气数据失败，请重试';
  } finally {
    weatherLoading.value = false;
  }
};

// 确保有会话ID
const ensureConversationId = async () => {
  if (!conversationId.value && currentUserId.value && currentUserId.value !== 'unknown_user') {
    try {
      const response = await createConversation({
        user_id: currentUserId.value,
      });

      if (response.success && response.conversation_id) {
        conversationId.value = response.conversation_id;
        console.log('✅ [weather.vue] 会话创建成功:', conversationId.value);
      } else {
        throw new Error('创建会话失败');
      }
    } catch (error) {
      console.error('❌ [weather.vue] 创建会话失败:', error);
      conversationId.value = `weather_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
  }
};

// 发送消息
const sendMessage = async (message: string) => {
  if (!currentUserId.value || !conversationId.value) {
    console.error('❌ [weather.vue] 缺少用户ID或会话ID');
    return;
  }

  isLoading.value = true;

  // 添加用户消息
  const userMessage: IChatStreamContent = {
    role: 'user',
    content: message,
    key: Date.now(),
    isFinish: true,
    reasoningData: { content: '', status: '' },
  };
  chatMessages.value.push(userMessage);

  // 添加AI消息占位符
  const aiMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 1,
    isFinish: false,
    reasoningData: { content: '', status: '' },
    preResponseContent: '',
  };
  chatMessages.value.push(aiMessage);

  // 创建新的AbortController
  abortController.value = new AbortController();

  try {
    await streamChat(
      {
        content: message,
        conversation_id: conversationId.value,
        user_id: currentUserId.value,
      },
      {
        onMessage: (content: string, isFinal: boolean) => {
          const lastMessage = chatMessages.value[chatMessages.value.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.content = content;
            lastMessage.isFinish = isFinal;
          }
        },
        onPreResponse: (content: string, _stage: string) => {
          const lastMessage = chatMessages.value[chatMessages.value.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.preResponseContent = content;
          }
        },
        onToolCall: (toolCall: IToolCall) => {
          console.log('🔧 [weather.vue] 工具调用:', toolCall);
          const lastMessage = chatMessages.value[chatMessages.value.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.isToolCallLoading = toolCall.status === 'start';
          }
        },
        onRecommendations: (recommendations: string[]) => {
          console.log('💡 [weather.vue] 推荐问题:', recommendations);
        },
        onEnd: () => {
          console.log('🏁 [weather.vue] 对话结束');
          isLoading.value = false;
        },
        onError: (error: Error) => {
          console.error('❌ [weather.vue] 对话错误:', error);
          isLoading.value = false;
          // 移除未完成的AI消息
          if (chatMessages.value.length > 0 && !chatMessages.value[chatMessages.value.length - 1].isFinish) {
            chatMessages.value.pop();
          }
        },
      },
      abortController.value.signal,
    );
  } catch (error) {
    console.error('❌ [weather.vue] 发送消息失败:', error);
    isLoading.value = false;
    // 移除未完成的AI消息
    if (chatMessages.value.length > 0 && !chatMessages.value[chatMessages.value.length - 1].isFinish) {
      chatMessages.value.pop();
    }
  }
};

// 组件挂载时初始化
onMounted(async () => {
  console.log('🚀 [weather.vue] 组件挂载');
  await loadUserInfo();
  // 自动加载天气数据
  await loadWeatherData();
});
</script>

<style scoped lang="scss">
.weather-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.weather-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

// 中间内容区域
.weather-content-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;

  // 加载状态
  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .loading-text {
      font-size: 20px;
      color: #666;
      text-align: center;
    }
  }

  // 天气内容区域
  .weather-sections {
    max-width: 1200px;
    margin: 0 auto;
  }

  // 错误状态
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 16px;

    .error-text {
      font-size: 18px;
      color: #e74c3c;
      text-align: center;
    }

    .retry-btn {
      padding: 12px 24px;
      background: #00bcd4;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      cursor: pointer;
      transition: background 0.3s ease;

      &:hover {
        background: #00acc1;
      }
    }
  }

  // 初始状态
  .initial-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 24px;

    .initial-text {
      font-size: 20px;
      color: #666;
      text-align: center;
    }

    .get-weather-btn {
      padding: 16px 32px;
      background: linear-gradient(135deg, #00bcd4, #00acc1);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 18px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 188, 212, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 188, 212, 0.4);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 底部区域样式
.footer {
  padding: 0;
  background: transparent;
  position: relative;
  z-index: 10002;
}

.input-wrapper {
  padding: 0 0px;
}

// 老董假装说话样式 - 参考chat.vue的样式
.laodong-fake-speaking {
  padding: 0px 20px;
  display: flex;
  justify-content: flex-start;
  // 透明背景，与inputBar保持一致
  background: transparent;
  border: 2px solid var(--border-accent); // 添加左右边框
  border-top: none; // 去掉上边框
  border-radius: 0 0 20px 20px; // 只有下方圆角，与输入框形成整体
  margin-top: -2px; // 与输入框无缝连接

  .fake-speaking-container {
    display: flex;
    align-items: center; // 改为居中对齐，一行显示
    gap: 8px; // 减少间距，从12px改为8px
    width: 100%; // 占满宽度

    .laodong-avatar {
      width: 45px; // 缩小头像，从80px改为54px（约缩小1/3）
      height: 45px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      border: 2px solid var(--border-accent);
      box-shadow: var(--shadow-medium);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .fake-speaking-content {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .fake-speaking-text {
        color: #000000; /* 改为黑色 */
        font-size: 28px; // 继续增大字体
        font-weight: 400;
        line-height: 1.2;
        white-space: nowrap; // 不换行
      }

      .fake-speaking-dots {
        display: flex;
        gap: 4px;
        align-items: center;

        .dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #000000; /* 改为黑色 */
          animation: fakeSpeakingPulse 1.5s ease-in-out infinite;

          &:nth-child(1) {
            animation-delay: 0s;
          }

          &:nth-child(2) {
            animation-delay: 0.3s;
          }

          &:nth-child(3) {
            animation-delay: 0.6s;
          }
        }
      }
    }
  }
}

// 假装说话动画
@keyframes fakeSpeakingPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .weather-content-container {
    padding: 16px;

    .loading-container .loading-text {
      font-size: 18px;
    }

    .error-container {
      .error-text {
        font-size: 16px;
      }

      .retry-btn {
        padding: 10px 20px;
        font-size: 14px;
      }
    }

    .initial-container {
      gap: 20px;

      .initial-text {
        font-size: 18px;
      }

      .get-weather-btn {
        padding: 14px 28px;
        font-size: 16px;
      }
    }
  }

  .laodong-fake-speaking {
    padding: 0px 16px; // 减少移动端上下padding

    .fake-speaking-container {
      gap: 6px; // 减少移动端间距

      .laodong-avatar {
        width: 42px; // 缩小移动端头像
        height: 42px;
      }

      .fake-speaking-content {
        gap: 6px;

        .fake-speaking-text {
          font-size: 26px; // 移动端也继续增大字体
          color: #000000; /* 移动端也改为黑色 */
        }

        .fake-speaking-dots {
          gap: 3px;

          .dot {
            width: 5px;
            height: 5px;
          }
        }
      }
    }
  }
}
</style>
